package com.study.basic.course.process_control;

import java.util.Scanner;

/**
 * 用户输入求和
 * 1、用户输入数字
 * 2、求和
 * 3、输出结果
 */
public class UserInputSum {
    // 用户输入数字，求和
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        double num, sum = 0.0;

        while(true){
            System.out.println("请输入数字：");
            num = scanner.nextDouble();
            // 如果输入的数字小于0，就结束输入
            if (num <0){
                break;
            }
            sum += num;
        }
        System.out.println(sum);
    }
}
