package com.study.basic.course.process_control;

public class LabeledBreak {
    // 带标签的break
    public static void main(String[] args) {
        // for 循环标记为first
        first:
        for (int i = 1; i<5; i++){
            // for 循环标记为second
            second:
            for (int j=1; j<3; j++){
                System.out.println("i=" + i + " j=" + j);
            }
            if (i == 2){
                //跳出循环
                break first;
            }
        }
    }
}
