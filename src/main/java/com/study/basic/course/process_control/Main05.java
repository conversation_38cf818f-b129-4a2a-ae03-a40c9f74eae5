package com.study.basic.course.process_control;

/**
 * Java继续和跳出循环
 * 1、continue
 * 2、break
 */
public class Main05 {
    public static void main(String[] args) {
        // for 循环
        for (int i =0; i<10; i++){
            if (i > 4 && i < 9){
                continue;
            }
            System.out.println("line" + i);
        }
        new Main05().main2();
        new  Main05().main3();
    }

    // for each循环
    public void main2(){
        int [] arr = {1,2,3,4,5,6};
        for (int i:arr){
            System.out.println(i);
        }
    }
    // break
    public void main3(){
        for (int i = 0; i<10; i++){
            System.out.println(i);
            if (i==3){
                break;
            }
        }
    }
}
