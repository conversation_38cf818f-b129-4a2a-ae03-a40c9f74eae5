package com.study.basic.course.process_control;

import java.util.Scanner;

/**
 * while循环
 * do while循环
 */
public class Loop {
    // while循环
    public static void main(String[] args) {
        int i = 0;
        while (i<10){
            System.out.println("line" + i);
            i++;
        }
        new Loop().main1();
        new Loop().main2();
    }

    public void main1(){
        int i = 100, sum = 0;
        while (i>0){
            sum = sum +i;
            --i;
        }
        System.out.println("sum=" + sum);
    }

    public void main2(){
        Scanner scanner = new Scanner(System.in);
        double num, sum = 0.0;
        do {
            System.out.println("请输入数字");
            num = scanner.nextDouble();
            sum = sum + num;
        }
        while (num!=0);
        System.out.println("sum=" + sum);
    }
}
