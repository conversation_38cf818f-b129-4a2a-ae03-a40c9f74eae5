package com.study.oop;

/**
 * 抽象类完整演示程序
 * 使用Person、Teacher、Student类展示抽象类的核心概念和实际应用
 */
public class AbstractClassDemo {
    
    public static void main(String[] args) {
        System.out.println("=== Java抽象类完整演示 ===\n");
        
        // 1. 抽象类基本概念演示
        demonstrateAbstractBasics();
        
        // 2. 抽象方法实现演示
        demonstrateAbstractMethods();
        
        // 3. 多态性演示
        demonstratePolymorphism();
        
        // 4. 抽象类的实际应用
        demonstratePracticalUsage();
        
        // 5. 抽象类与普通类的对比
        demonstrateComparison();
    }
    
    /**
     * 1. 抽象类基本概念演示
     */
    public static void demonstrateAbstractBasics() {
        System.out.println("1. === 抽象类基本概念演示 ===");
        
        System.out.println("抽象类的特点：");
        System.out.println("❌ 不能直接实例化");
        System.out.println("✅ 可以有构造方法");
        System.out.println("✅ 可以有普通方法和抽象方法");
        System.out.println("✅ 可以有属性和静态方法");
        System.out.println("✅ 子类必须实现所有抽象方法");
        
        // ❌ 不能直接实例化抽象类
        // Person person = new Person("张三", 25);  // 编译错误
        
        // ✅ 可以实例化具体子类
        System.out.println("\n--- 创建具体子类对象 ---");
        Teacher teacher = new Teacher("李老师", 35, "数学");
        Student student = new Student("小明", 20, "计算机科学");
        
        System.out.println("\n--- 观察构造方法调用链 ---");
        System.out.println("创建Teacher对象时：");
        System.out.println("1. 先调用Person抽象类的构造方法");
        System.out.println("2. 再调用Teacher子类的构造方法");
        
        System.out.println("\n" + "=".repeat(50) + "\n");
    }
    
    /**
     * 2. 抽象方法实现演示
     */
    public static void demonstrateAbstractMethods() {
        System.out.println("2. === 抽象方法实现演示 ===");
        
        Teacher teacher = new Teacher("王老师", 40, "男", "北京市", "13800138000",
                                    "物理", "清华大学", 15, 8000.0, "大学");
        Student student = new Student("小红", 19, "女", "上海市", "13900139000",
                                    "软件工程", "北京大学", 2, "大学生");
        
        System.out.println("--- Person抽象类定义了4个抽象方法 ---");
        System.out.println("1. work() - 工作方法");
        System.out.println("2. study() - 学习方法");
        System.out.println("3. getProfession() - 获取职业描述");
        System.out.println("4. calculateIncome() - 计算收入");
        
        System.out.println("\n--- Teacher类的实现 ---");
        teacher.work();           // Teacher的工作实现
        teacher.study();          // Teacher的学习实现
        System.out.println("职业：" + teacher.getProfession());
        System.out.println("收入：" + teacher.calculateIncome() + " 元/月");
        
        System.out.println("\n--- Student类的实现 ---");
        student.work();           // Student的工作实现（学习）
        student.study();          // Student的学习实现
        System.out.println("身份：" + student.getProfession());
        System.out.println("奖学金：" + student.calculateIncome() + " 元");
        
        System.out.println("\n说明：同一个抽象方法在不同子类中有不同的实现");
        
        System.out.println("\n" + "=".repeat(50) + "\n");
    }
    
    /**
     * 3. 多态性演示
     */
    public static void demonstratePolymorphism() {
        System.out.println("3. === 多态性演示 ===");
        
        // 使用抽象类引用指向具体子类对象
        Person[] people = {
            new Teacher("张教授", 45, "男", "北京", "13700137000", 
                       "计算机", "北京理工大学", 20, 12000.0, "大学"),
            new Student("李同学", 21, "女", "广州", "13600136000",
                       "电子工程", "华南理工大学", 3, "大学生"),
            new Teacher("陈老师", 32, "女", "深圳", "13500135000",
                       "英语", "深圳中学", 8, 6000.0, "中学"),
            new Student("王同学", 18, "男", "成都", "13400134000",
                       "机械工程", "四川大学", 1, "大学生")
        };
        
        System.out.println("--- 多态调用演示 ---");
        for (int i = 0; i < people.length; i++) {
            System.out.println("\n处理第" + (i + 1) + "个人员：");
            Person person = people[i];
            
            // 多态调用 - 运行时决定调用哪个类的方法
            person.introduce();      // 调用重写的方法
            person.work();          // 调用抽象方法的具体实现
            System.out.println("职业/身份：" + person.getProfession());
            System.out.println("收入/奖学金：" + person.calculateIncome() + " 元");
            
            // 类型检查和转换
            if (person instanceof Teacher) {
                System.out.println("这是一位老师，可以调用老师特有的方法：");
                Teacher teacher = (Teacher) person;
                teacher.prepareLessons();
                teacher.earnQualification("高级教师资格证");
            } else if (person instanceof Student) {
                System.out.println("这是一位学生，可以调用学生特有的方法：");
                Student student = (Student) person;
                student.enrollCourse("高等数学");
                student.takeExam("高等数学", 85.0);
            }
        }
        
        System.out.println("\n多态性的优势：");
        System.out.println("- 统一的接口处理不同类型的对象");
        System.out.println("- 代码更加灵活和可扩展");
        System.out.println("- 运行时动态绑定方法");
        
        System.out.println("\n" + "=".repeat(50) + "\n");
    }
    
    /**
     * 4. 抽象类的实际应用
     */
    public static void demonstratePracticalUsage() {
        System.out.println("4. === 抽象类实际应用演示 ===");
        
        // 创建一个教育管理系统场景
        System.out.println("--- 教育管理系统 ---");
        
        Teacher mathTeacher = new Teacher("数学张老师", 38, "数学");
        Teacher englishTeacher = new Teacher("英语李老师", 35, "英语");
        Student student1 = new Student("优秀生小明", 20, "计算机科学");
        Student student2 = new Student("努力生小红", 19, "数学");
        
        // 教师工作流程
        System.out.println("\n=== 教师工作日常 ===");
        mathTeacher.prepareLessons();
        mathTeacher.teachClass("高三(1)班");
        mathTeacher.gradeHomework(30);
        mathTeacher.attendTraining("新课程标准");
        mathTeacher.earnQualification("特级教师");
        
        // 学生学习流程
        System.out.println("\n=== 学生学习日常 ===");
        student1.enrollCourse("高等数学");
        student1.enrollCourse("数据结构");
        student1.enrollCourse("算法设计");
        student1.attendClass("高等数学");
        student1.doHomework("数据结构");
        student1.takeExam("高等数学", 92.0);
        student1.applyScholarship("优秀学生奖学金", 2000.0);
        
        // 师生互动
        System.out.println("\n=== 师生互动 ===");
        mathTeacher.mentorStudent("小明", "高等数学难点");
        
        // 学生互动
        System.out.println("\n=== 学生协作 ===");
        student1.collaborateWith(student2, "数学建模竞赛");
        
        // 显示详细信息
        System.out.println("\n=== 人员信息汇总 ===");
        mathTeacher.displayInfo();
        student1.displayInfo();
        
        // 统计信息
        System.out.println("\n=== 统计信息 ===");
        System.out.println("总人数：" + Person.getTotalPersons());
        System.out.println("教师总数：" + Teacher.getTotalTeachers());
        System.out.println("学生总数：" + Student.getTotalStudents());
        
        System.out.println("\n" + "=".repeat(50) + "\n");
    }
    
    /**
     * 5. 抽象类与普通类的对比
     */
    public static void demonstrateComparison() {
        System.out.println("5. === 抽象类与普通类对比 ===");
        
        System.out.println("抽象类 vs 普通类：");
        System.out.println();
        
        System.out.println("📋 抽象类特点：");
        System.out.println("✅ 使用abstract关键字修饰");
        System.out.println("❌ 不能被实例化");
        System.out.println("✅ 可以包含抽象方法");
        System.out.println("✅ 可以包含普通方法");
        System.out.println("✅ 可以有构造方法");
        System.out.println("✅ 子类必须实现所有抽象方法");
        System.out.println("✅ 适合作为基类使用");
        
        System.out.println("\n📋 普通类特点：");
        System.out.println("❌ 不使用abstract关键字");
        System.out.println("✅ 可以被实例化");
        System.out.println("❌ 不能包含抽象方法");
        System.out.println("✅ 只能包含普通方法");
        System.out.println("✅ 可以有构造方法");
        System.out.println("❌ 子类可以选择性重写方法");
        System.out.println("✅ 可以直接使用");
        
        System.out.println("\n🎯 使用场景：");
        System.out.println("抽象类适用于：");
        System.out.println("- 多个类有共同的属性和方法");
        System.out.println("- 需要强制子类实现某些方法");
        System.out.println("- 想要提供一些公共实现");
        System.out.println("- 建立类的层次结构");
        
        System.out.println("\n普通类适用于：");
        System.out.println("- 完整的功能实现");
        System.out.println("- 可以直接使用的类");
        System.out.println("- 不需要强制子类实现特定方法");
        
        System.out.println("\n💡 设计建议：");
        System.out.println("- 当多个类有共同行为但实现不同时，使用抽象类");
        System.out.println("- 当需要提供模板方法模式时，使用抽象类");
        System.out.println("- 当类的功能完整且可以直接使用时，使用普通类");
        
        System.out.println("\n" + "=".repeat(50));
        System.out.println("=== 抽象类演示完成 ===");
    }
}
