package com.study.oop;

/**
 * 面向对象编程基础演示
 * 演示类和对象的创建、使用以及面向对象的基本概念
 */
public class Lamp {
    // 实例变量
    private boolean isOn;

    public static void main(String[] args) {
        // 创建对象
        Lamp lamp = new Lamp();
        // 调用方法
        lamp.turnOff();

    }

    // 方法
    public void turnOn(){
        // 修改实例变量
        isOn = true;
        System.out.println("灯亮了");
    }

    // 方法
    public  void turnOff(){
        // 修改实例变量
        isOn = false;
        System.out.println("灯灭了");
    }

    /**
     * Java中的方法
     * 1、方法是类中的一个行为
     *   public void methodName() {
     *       // 方法体
     *   }
     * 2、方法可以有返回值，也可以没有返回值
     * 3、方法可以有参数，也可以没有参数
     * 4、方法可以被重载
     * 5、方法可以被重写
     */
    public void 

}
