package com.study.oop;

/**
 * Person抽象类 - 演示Java抽象类的完整特性
 *
 * 抽象类的特点：
 * 1. 使用abstract关键字修饰
 * 2. 不能被实例化（不能用new创建对象）
 * 3. 可以包含抽象方法和普通方法
 * 4. 可以有构造方法、属性、静态方法
 * 5. 子类必须实现所有抽象方法
 * 6. 抽象类可以继承其他类，也可以实现接口
 *
 * 使用场景：
 * - 多个类有共同的属性和方法
 * - 需要强制子类实现某些方法
 * - 想要提供一些公共的实现，同时保留一些抽象的行为
 */
public abstract class Person {

    // ========== 属性定义 ==========

    // protected修饰符：子类可以直接访问
    protected String name;          // 姓名
    protected int age;             // 年龄
    protected String gender;       // 性别
    protected String address;      // 地址
    protected String phoneNumber;  // 电话号码

    // private修饰符：只有当前类可以访问
    private String id;            // 身份证号
    private boolean isAlive;      // 是否在世

    // static修饰符：类变量，所有实例共享
    private static int totalPersons = 0;  // 人员总数

    // final修饰符：常量
    public static final int ADULT_AGE = 18;  // 成年年龄

    // ========== 构造方法 ==========

    /**
     * 无参构造方法
     */
    public Person() {
        this("未知", 0, "未知", "未知地址", "未知电话");
        System.out.println("Person无参构造方法被调用");
    }

    /**
     * 基本构造方法
     */
    public Person(String name, int age) {
        this(name, age, "未知", "未知地址", "未知电话");
        System.out.println("Person基本构造方法被调用: " + name);
    }

    /**
     * 完整构造方法
     */
    public Person(String name, int age, String gender, String address, String phoneNumber) {
        this.name = name;
        this.age = age;
        this.gender = gender;
        this.address = address;
        this.phoneNumber = phoneNumber;
        this.id = generateId();
        this.isAlive = true;
        totalPersons++;
        System.out.println("Person完整构造方法被调用: " + name + " (" + gender + ")");
    }

    // ========== 抽象方法 - 子类必须实现 ==========

    /**
     * 抽象方法：工作
     * 不同类型的人有不同的工作方式，子类必须实现
     */
    public abstract void work();

    /**
     * 抽象方法：学习
     * 不同类型的人有不同的学习方式，子类必须实现
     */
    public abstract void study();

    /**
     * 抽象方法：获取职业描述
     * 每个子类都应该提供自己的职业描述
     */
    public abstract String getProfession();

    /**
     * 抽象方法：计算收入
     * 不同职业的收入计算方式不同
     */
    public abstract double calculateIncome();

    // ========== 普通方法 - 提供公共实现 ==========

    /**
     * 吃饭 - 所有人都需要吃饭，提供通用实现
     */
    public void eat() {
        System.out.println(name + " 正在吃饭");
    }

    /**
     * 吃特定食物 - 方法重载
     */
    public void eat(String food) {
        System.out.println(name + " 正在吃 " + food);
    }

    /**
     * 睡觉 - 所有人都需要睡觉
     */
    public void sleep() {
        System.out.println(name + " 正在睡觉");
    }

    /**
     * 睡觉指定时间 - 方法重载
     */
    public void sleep(int hours) {
        System.out.println(name + " 睡了 " + hours + " 小时");
    }

    /**
     * 说话 - 基本的说话功能
     */
    public void speak(String message) {
        System.out.println(name + " 说：" + message);
    }

    /**
     * 走路 - 基本的移动功能
     */
    public void walk() {
        System.out.println(name + " 正在走路");
    }

    /**
     * 自我介绍 - 基本介绍，子类可以重写
     */
    public void introduce() {
        System.out.println("大家好，我是 " + name + "，今年 " + age + " 岁，" +
                          "性别 " + gender + "，我是一名 " + getProfession());
    }

    /**
     * 庆祝生日 - 年龄增长
     */
    public void celebrateBirthday() {
        age++;
        System.out.println("🎉 " + name + " 生日快乐！现在 " + age + " 岁了");
        if (age == ADULT_AGE) {
            System.out.println("🎓 " + name + " 已经成年了！");
        }
    }

    /**
     * 检查是否成年
     */
    public boolean isAdult() {
        return age >= ADULT_AGE;
    }

    /**
     * 显示个人信息
     */
    public void displayInfo() {
        System.out.println("========== 个人信息 ==========");
        System.out.println("身份证号：" + id);
        System.out.println("姓名：" + name);
        System.out.println("年龄：" + age + " 岁 " + (isAdult() ? "(成年)" : "(未成年)"));
        System.out.println("性别：" + gender);
        System.out.println("地址：" + address);
        System.out.println("电话：" + phoneNumber);
        System.out.println("职业：" + getProfession());
        System.out.println("收入：" + calculateIncome() + " 元/月");
        System.out.println("状态：" + (isAlive ? "在世" : "已故"));
        System.out.println("=============================");
    }

    // ========== 工具方法 ==========

    /**
     * 生成身份证号（简化版）
     */
    private String generateId() {
        return "ID" + String.format("%06d", totalPersons);
    }

    /**
     * 验证年龄是否合理
     */
    public boolean isValidAge() {
        return age >= 0 && age <= 150;
    }

    /**
     * 计算到退休还有多少年（假设65岁退休）
     */
    public int yearsToRetirement() {
        int retirementAge = 65;
        return Math.max(0, retirementAge - age);
    }

    // ========== Getter和Setter方法 ==========

    public String getName() {
        return name;
    }

    public void setName(String name) {
        if (name != null && !name.trim().isEmpty()) {
            this.name = name;
        } else {
            System.out.println("姓名不能为空！");
        }
    }

    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        if (age >= 0 && age <= 150) {
            this.age = age;
        } else {
            System.out.println("年龄必须在0-150之间！");
        }
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getId() {
        return id;
    }

    public boolean isAlive() {
        return isAlive;
    }

    public void setAlive(boolean alive) {
        this.isAlive = alive;
    }

    // ========== 静态方法 ==========

    /**
     * 获取总人数
     */
    public static int getTotalPersons() {
        return totalPersons;
    }

    /**
     * 比较两个人的年龄
     */
    public static void compareAge(Person person1, Person person2) {
        System.out.println("\n=== 年龄比较 ===");
        if (person1.age > person2.age) {
            System.out.println(person1.name + " 比 " + person2.name + " 年龄大");
        } else if (person1.age < person2.age) {
            System.out.println(person2.name + " 比 " + person1.name + " 年龄大");
        } else {
            System.out.println(person1.name + " 和 " + person2.name + " 年龄相同");
        }
    }

    // ========== final方法 - 不能被重写 ==========

    /**
     * final方法：获取完整姓名（不能被子类重写）
     */
    public final String getFullInfo() {
        return name + " (" + age + "岁, " + gender + ", " + getProfession() + ")";
    }

    // ========== Object类方法重写 ==========

    @Override
    public String toString() {
        return "Person{" +
                "name='" + name + '\'' +
                ", age=" + age +
                ", gender='" + gender + '\'' +
                ", profession='" + getProfession() + '\'' +
                ", id='" + id + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;

        Person person = (Person) obj;
        return id.equals(person.id);  // 根据身份证号判断是否为同一人
    }
}
