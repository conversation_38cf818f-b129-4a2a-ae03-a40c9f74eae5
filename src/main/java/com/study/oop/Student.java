package com.study.oop;

/**
 * Student类 - 继承自Person抽象类
 * 演示抽象类的另一个具体实现
 * 
 * 与Teacher类一样，Student也必须实现Person的所有抽象方法
 */
public class Student extends Person {
    
    // ========== 子类特有属性 ==========
    
    private String studentId;        // 学号
    private String major;           // 专业
    private String school;          // 学校
    private int grade;              // 年级
    private double gpa;             // 平均绩点
    private String[] courses;       // 选修课程
    private int courseCount;        // 课程数量
    private double scholarship;     // 奖学金
    private String studentLevel;    // 学生级别（小学生/中学生/高中生/大学生）
    
    // 静态变量
    private static int totalStudents = 0;
    
    // ========== 构造方法 ==========
    
    /**
     * 基本构造方法
     */
    public Student(String name, int age, String major) {
        super(name, age, "未知", "未知地址", "未知电话");
        this.studentId = generateStudentId();
        this.major = major;
        this.school = "未知学校";
        this.grade = 1;
        this.gpa = 0.0;
        this.courses = new String[20];
        this.courseCount = 0;
        this.scholarship = 0.0;
        this.studentLevel = "大学生";
        totalStudents++;
        System.out.println("Student基本构造方法被调用: " + name + " (" + major + "专业)");
    }
    
    /**
     * 完整构造方法
     */
    public Student(String name, int age, String gender, String address, String phoneNumber,
                   String major, String school, int grade, String studentLevel) {
        super(name, age, gender, address, phoneNumber);
        this.studentId = generateStudentId();
        this.major = major;
        this.school = school;
        this.grade = grade;
        this.gpa = 0.0;
        this.courses = new String[20];
        this.courseCount = 0;
        this.scholarship = 0.0;
        this.studentLevel = studentLevel;
        totalStudents++;
        System.out.println("Student完整构造方法被调用: " + name + " (" + major + "专业)");
    }
    
    // ========== 实现抽象方法 ==========
    
    /**
     * 实现抽象方法：工作
     * 对于学生来说，"工作"就是学习
     */
    @Override
    public void work() {
        System.out.println("学生 " + name + " 正在努力学习 " + major + " 专业课程");
        System.out.println("学习内容：上课听讲、完成作业、复习功课、准备考试");
    }
    
    /**
     * 实现抽象方法：学习
     */
    @Override
    public void study() {
        System.out.println("学生 " + name + " 正在专心学习");
        System.out.println("学习方式：课堂学习、自主学习、小组讨论、实践操作");
    }
    
    /**
     * 实现抽象方法：获取职业描述
     */
    @Override
    public String getProfession() {
        return major + "专业" + studentLevel + " (在读)";
    }
    
    /**
     * 实现抽象方法：计算收入
     * 学生的"收入"主要是奖学金
     */
    @Override
    public double calculateIncome() {
        // 基础奖学金 + GPA奖励 + 课程数量奖励
        double gpaBonus = gpa * 500;  // GPA每1分奖励500元
        double courseBonus = courseCount * 50;  // 每门课程50元奖励
        return scholarship + gpaBonus + courseBonus;
    }
    
    // ========== 重写父类方法 ==========
    
    /**
     * 重写自我介绍方法
     */
    @Override
    public void introduce() {
        System.out.println("大家好，我是 " + name + "，今年 " + age + " 岁，" +
                          "是 " + school + " " + major + " 专业 " + grade + " 年级的学生，" +
                          "学号是 " + studentId);
    }
    
    /**
     * 重写显示信息方法
     */
    @Override
    public void displayInfo() {
        System.out.println("========== 学生信息 ==========");
        System.out.println("身份证号：" + getId());
        System.out.println("学号：" + studentId);
        System.out.println("姓名：" + name);
        System.out.println("年龄：" + age + " 岁 " + (isAdult() ? "(成年)" : "(未成年)"));
        System.out.println("性别：" + gender);
        System.out.println("地址：" + address);
        System.out.println("电话：" + phoneNumber);
        System.out.println("身份：" + getProfession());
        System.out.println("学校：" + school);
        System.out.println("专业：" + major);
        System.out.println("年级：" + grade + " 年级");
        System.out.println("学生级别：" + studentLevel);
        System.out.println("平均绩点：" + String.format("%.2f", gpa));
        System.out.println("选修课程：" + courseCount + " 门");
        if (courseCount > 0) {
            System.out.print("课程列表：");
            for (int i = 0; i < courseCount; i++) {
                System.out.print(courses[i]);
                if (i < courseCount - 1) System.out.print(", ");
            }
            System.out.println();
        }
        System.out.println("基础奖学金：" + scholarship + " 元");
        System.out.println("总奖学金：" + calculateIncome() + " 元");
        System.out.println("状态：" + (isAlive() ? "在读" : "已毕业"));
        System.out.println("=============================");
    }
    
    // ========== 子类特有方法 ==========
    
    /**
     * 上课
     */
    public void attendClass(String courseName) {
        System.out.println("学生 " + name + " 正在上 " + courseName + " 课");
        study(); // 调用学习方法
    }
    
    /**
     * 做作业
     */
    public void doHomework(String subject) {
        System.out.println("学生 " + name + " 正在做 " + subject + " 作业");
        System.out.println("作业内容：认真完成老师布置的练习题");
    }
    
    /**
     * 参加考试
     */
    public void takeExam(String subject, double score) {
        System.out.println("学生 " + name + " 参加了 " + subject + " 考试");
        System.out.println("考试成绩：" + score + " 分");
        
        if (score >= 90) {
            System.out.println("🎉 优秀！继续保持！");
        } else if (score >= 80) {
            System.out.println("👍 良好！再接再厉！");
        } else if (score >= 70) {
            System.out.println("😊 中等，还有提升空间");
        } else if (score >= 60) {
            System.out.println("😐 及格，需要更加努力");
        } else {
            System.out.println("😞 不及格，需要补考");
        }
        
        updateGPA(score);
    }
    
    /**
     * 选课
     */
    public void enrollCourse(String courseName) {
        if (courseCount < courses.length) {
            courses[courseCount] = courseName;
            courseCount++;
            System.out.println("✅ " + name + " 成功选修了 " + courseName + " 课程");
            System.out.println("现在总共选修了 " + courseCount + " 门课程");
        } else {
            System.out.println("❌ 选课数量已达上限，无法选修更多课程");
        }
    }
    
    /**
     * 退课
     */
    public void dropCourse(String courseName) {
        boolean found = false;
        for (int i = 0; i < courseCount; i++) {
            if (courses[i].equals(courseName)) {
                // 将后面的课程前移
                for (int j = i; j < courseCount - 1; j++) {
                    courses[j] = courses[j + 1];
                }
                courseCount--;
                found = true;
                System.out.println("✅ " + name + " 成功退选了 " + courseName + " 课程");
                break;
            }
        }
        if (!found) {
            System.out.println("❌ 没有找到 " + courseName + " 课程，无法退选");
        }
    }
    
    /**
     * 申请奖学金
     */
    public void applyScholarship(String scholarshipName, double amount) {
        if (gpa >= 3.5) {
            scholarship += amount;
            System.out.println("🎉 " + name + " 成功获得 " + scholarshipName + " 奖学金 " + amount + " 元！");
            System.out.println("总奖学金：" + scholarship + " 元");
        } else {
            System.out.println("❌ GPA不足3.5，无法申请 " + scholarshipName + " 奖学金");
        }
    }
    
    /**
     * 参加社团活动
     */
    public void joinClubActivity(String clubName, String activity) {
        System.out.println("学生 " + name + " 参加了 " + clubName + " 的 " + activity + " 活动");
        System.out.println("活动收获：锻炼能力、结交朋友、丰富经历");
    }
    
    /**
     * 实习
     */
    public void internship(String company, String position) {
        System.out.println("学生 " + name + " 在 " + company + " 进行 " + position + " 实习");
        System.out.println("实习内容：理论联系实际、积累工作经验");
    }
    
    /**
     * 与同学协作
     */
    public void collaborateWith(Student otherStudent, String project) {
        System.out.println("学生 " + name + " 与 " + otherStudent.getName() + " 合作完成 " + project);
        System.out.println("合作内容：分工协作、互相学习、共同进步");
    }
    
    // ========== 工具方法 ==========
    
    /**
     * 生成学号
     */
    private String generateStudentId() {
        return "STU" + String.format("%06d", totalStudents + 1);
    }
    
    /**
     * 更新GPA
     */
    private void updateGPA(double newScore) {
        // 简化的GPA计算：新成绩与当前GPA的加权平均
        if (courseCount > 0) {
            gpa = (gpa * (courseCount - 1) + (newScore / 25.0)) / courseCount;  // 100分制转4分制
        } else {
            gpa = newScore / 25.0;
        }
        System.out.println("更新后的GPA：" + String.format("%.2f", gpa));
    }
    
    /**
     * 检查是否可以毕业
     */
    public boolean canGraduate() {
        return courseCount >= 8 && gpa >= 2.0;  // 至少8门课程且GPA不低于2.0
    }
    
    // ========== Getter和Setter方法 ==========
    
    public String getStudentId() {
        return studentId;
    }
    
    public String getMajor() {
        return major;
    }
    
    public void setMajor(String major) {
        this.major = major;
    }
    
    public String getSchool() {
        return school;
    }
    
    public void setSchool(String school) {
        this.school = school;
    }
    
    public int getGrade() {
        return grade;
    }
    
    public void setGrade(int grade) {
        if (grade >= 1 && grade <= 4) {
            this.grade = grade;
        } else {
            System.out.println("年级必须在1-4之间！");
        }
    }
    
    public double getGpa() {
        return gpa;
    }
    
    public double getScholarship() {
        return scholarship;
    }
    
    public String getStudentLevel() {
        return studentLevel;
    }
    
    public void setStudentLevel(String studentLevel) {
        this.studentLevel = studentLevel;
    }
    
    public int getCourseCount() {
        return courseCount;
    }
    
    public static int getTotalStudents() {
        return totalStudents;
    }
    
    // ========== Object类方法重写 ==========
    
    @Override
    public String toString() {
        return "Student{" +
                "name='" + name + '\'' +
                ", age=" + age +
                ", studentId='" + studentId + '\'' +
                ", major='" + major + '\'' +
                ", school='" + school + '\'' +
                ", grade=" + grade +
                ", gpa=" + String.format("%.2f", gpa) +
                '}';
    }
}
