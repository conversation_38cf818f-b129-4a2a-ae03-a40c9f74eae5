package com.study.oop;

/**
 * Teacher类 - 继承自Person抽象类
 * 演示抽象类的具体实现
 *
 * 作为Person的子类，Teacher必须：
 * 1. 实现所有抽象方法
 * 2. 可以重写普通方法
 * 3. 可以添加自己特有的属性和方法
 */
public class Teacher extends Person {

    // ========== 子类特有属性 ==========

    private String subject;           // 教授科目
    private String school;           // 所在学校
    private int teachingYears;       // 教学年限
    private double salary;           // 月薪
    private String[] qualifications; // 资格证书
    private int qualificationCount;  // 证书数量
    private String teachingLevel;    // 教学级别（小学/中学/高中/大学）

    // 静态变量
    private static int totalTeachers = 0;

    // ========== 构造方法 ==========

    /**
     * 基本构造方法
     */
    public Teacher(String name, int age, String subject) {
        super(name, age, "未知", "未知地址", "未知电话");
        this.subject = subject;
        this.school = "未知学校";
        this.teachingYears = 0;
        this.salary = 5000.0;
        this.qualifications = new String[10];
        this.qualificationCount = 0;
        this.teachingLevel = "中学";
        totalTeachers++;
        System.out.println("Teacher基本构造方法被调用: " + name + " (" + subject + "老师)");
    }

    /**
     * 完整构造方法
     */
    public Teacher(String name, int age, String gender, String address, String phoneNumber,
                   String subject, String school, int teachingYears, double salary, String teachingLevel) {
        super(name, age, gender, address, phoneNumber);
        this.subject = subject;
        this.school = school;
        this.teachingYears = teachingYears;
        this.salary = salary;
        this.qualifications = new String[10];
        this.qualificationCount = 0;
        this.teachingLevel = teachingLevel;
        totalTeachers++;
        System.out.println("Teacher完整构造方法被调用: " + name + " (" + subject + "老师)");
    }

    // ========== 实现抽象方法 ==========

    /**
     * 实现抽象方法：工作
     */
    @Override
    public void work() {
        System.out.println(name + " 老师正在教授 " + subject + " 课程");
        System.out.println("课堂内容：讲解知识点、布置作业、答疑解惑");
    }

    /**
     * 实现抽象方法：学习
     */
    @Override
    public void study() {
        System.out.println(name + " 老师正在学习新的教学方法和 " + subject + " 专业知识");
        System.out.println("学习内容：教育理论、学科前沿、教学技巧");
    }

    /**
     * 实现抽象方法：获取职业描述
     */
    @Override
    public String getProfession() {
        return subject + "老师 (" + teachingLevel + ")";
    }

    /**
     * 实现抽象方法：计算收入
     */
    @Override
    public double calculateIncome() {
        // 基本工资 + 教学年限奖金 + 资格证书奖金
        double yearBonus = teachingYears * 200;  // 每年教学经验200元奖金
        double certBonus = qualificationCount * 300;  // 每个证书300元奖金
        return salary + yearBonus + certBonus;
    }

    // ========== 重写父类方法 ==========

    /**
     * 重写自我介绍方法
     */
    @Override
    public void introduce() {
        System.out.println("大家好，我是 " + name + " 老师，今年 " + age + " 岁，" +
                          "在 " + school + " 教授 " + subject + "，" +
                          "有 " + teachingYears + " 年教学经验");
    }

    /**
     * 重写显示信息方法
     */
    @Override
    public void displayInfo() {
        System.out.println("========== 教师信息 ==========");
        System.out.println("身份证号：" + getId());
        System.out.println("姓名：" + name);
        System.out.println("年龄：" + age + " 岁 " + (isAdult() ? "(成年)" : "(未成年)"));
        System.out.println("性别：" + gender);
        System.out.println("地址：" + address);
        System.out.println("电话：" + phoneNumber);
        System.out.println("职业：" + getProfession());
        System.out.println("学校：" + school);
        System.out.println("教授科目：" + subject);
        System.out.println("教学级别：" + teachingLevel);
        System.out.println("教学年限：" + teachingYears + " 年");
        System.out.println("基本工资：" + salary + " 元/月");
        System.out.println("实际收入：" + calculateIncome() + " 元/月");
        System.out.println("资格证书：" + qualificationCount + " 个");
        if (qualificationCount > 0) {
            System.out.print("证书列表：");
            for (int i = 0; i < qualificationCount; i++) {
                System.out.print(qualifications[i]);
                if (i < qualificationCount - 1) System.out.print(", ");
            }
            System.out.println();
        }
        System.out.println("状态：" + (isAlive() ? "在职" : "已离职"));
        System.out.println("=============================");
    }

    // ========== 子类特有方法 ==========

    /**
     * 备课
     */
    public void prepareLessons() {
        System.out.println(name + " 老师正在为 " + subject + " 课程备课");
        System.out.println("备课内容：制作课件、准备教具、设计教学活动");
    }

    /**
     * 上课
     */
    public void teachClass(String className) {
        System.out.println(name + " 老师正在给 " + className + " 上 " + subject + " 课");
        work(); // 调用工作方法
    }

    /**
     * 批改作业
     */
    public void gradeHomework(int studentCount) {
        System.out.println(name + " 老师正在批改 " + studentCount + " 份 " + subject + " 作业");
        System.out.println("批改进度：认真检查每一份作业，给出评语和建议");
    }

    /**
     * 家长会
     */
    public void parentMeeting() {
        System.out.println(name + " 老师正在主持家长会");
        System.out.println("会议内容：汇报学生学习情况，与家长沟通交流");
    }

    /**
     * 参加培训
     */
    public void attendTraining(String trainingTopic) {
        System.out.println(name + " 老师参加了关于 " + trainingTopic + " 的培训");
        study(); // 调用学习方法
    }

    /**
     * 获得资格证书
     */
    public void earnQualification(String qualification) {
        if (qualificationCount < qualifications.length) {
            qualifications[qualificationCount] = qualification;
            qualificationCount++;
            System.out.println("🎉 " + name + " 老师获得了 " + qualification + " 资格证书！");
            System.out.println("现在总共有 " + qualificationCount + " 个证书");
        } else {
            System.out.println("证书存储已满，无法添加更多证书");
        }
    }

    /**
     * 指导学生
     */
    public void mentorStudent(String studentName, String topic) {
        System.out.println(name + " 老师正在指导学生 " + studentName + " 学习 " + topic);
        System.out.println("指导内容：答疑解惑、学习方法指导、学习计划制定");
    }

    /**
     * 参与教研活动
     */
    public void participateInResearch(String researchTopic) {
        System.out.println(name + " 老师参与了关于 " + researchTopic + " 的教研活动");
        System.out.println("活动内容：教学方法研讨、课程改革、经验分享");
    }

    /**
     * 评估教学效果
     */
    public void evaluateTeaching() {
        System.out.println(name + " 老师正在评估 " + subject + " 课程的教学效果");
        System.out.println("评估方式：学生反馈、考试成绩分析、同行评议");
    }

    /**
     * 与其他老师协作
     */
    public void collaborateWith(Teacher otherTeacher) {
        System.out.println(name + " 老师与 " + otherTeacher.getName() + " 老师进行教学协作");
        System.out.println("协作内容：跨学科教学、经验交流、共同备课");
    }

    // ========== Getter和Setter方法 ==========

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getSchool() {
        return school;
    }

    public void setSchool(String school) {
        this.school = school;
    }

    public int getTeachingYears() {
        return teachingYears;
    }

    public void setTeachingYears(int teachingYears) {
        if (teachingYears >= 0) {
            this.teachingYears = teachingYears;
        } else {
            System.out.println("教学年限不能为负数！");
        }
    }

    public double getSalary() {
        return salary;
    }

    public void setSalary(double salary) {
        if (salary >= 0) {
            this.salary = salary;
        } else {
            System.out.println("工资不能为负数！");
        }
    }

    public String getTeachingLevel() {
        return teachingLevel;
    }

    public void setTeachingLevel(String teachingLevel) {
        this.teachingLevel = teachingLevel;
    }

    public int getQualificationCount() {
        return qualificationCount;
    }

    public static int getTotalTeachers() {
        return totalTeachers;
    }

    // ========== Object类方法重写 ==========

    @Override
    public String toString() {
        return "Teacher{" +
                "name='" + name + '\'' +
                ", age=" + age +
                ", subject='" + subject + '\'' +
                ", school='" + school + '\'' +
                ", teachingYears=" + teachingYears +
                ", salary=" + salary +
                '}';
    }

    // ========== 主方法 - 演示Teacher类 ==========

    /**
     * 主方法 - 演示Teacher类的功能
     */
    public static void main(String[] args) {
        System.out.println("=== Teacher类功能演示 ===\n");

        // 1. 创建不同的教师对象
        System.out.println("1. 创建教师对象：");
        Teacher teacher1 = new Teacher("张老师", 35, "数学");
        Teacher teacher2 = new Teacher("李老师", 40, "女", "北京市海淀区", "13800138000",
                                     "英语", "北京中学", 15, 8000.0, "中学");

        // 2. 演示抽象方法的实现
        System.out.println("\n2. 抽象方法实现演示：");
        System.out.println("--- 工作方法 ---");
        teacher1.work();

        System.out.println("\n--- 学习方法 ---");
        teacher1.study();

        System.out.println("\n--- 职业描述 ---");
        System.out.println("职业：" + teacher1.getProfession());

        System.out.println("\n--- 收入计算 ---");
        System.out.println("收入：" + teacher1.calculateIncome() + " 元/月");

        // 3. 演示继承的方法
        System.out.println("\n3. 继承方法演示：");
        teacher1.introduce();
        teacher1.eat("午餐");
        teacher1.sleep(8);
        teacher1.speak("同学们，我们开始上课！");

        // 4. 演示教师特有的方法
        System.out.println("\n4. 教师特有方法演示：");
        teacher2.prepareLessons();
        teacher2.teachClass("高三(2)班");
        teacher2.gradeHomework(35);
        teacher2.parentMeeting();
        teacher2.attendTraining("新课程改革");

        // 5. 演示资格证书系统
        System.out.println("\n5. 资格证书演示：");
        teacher2.earnQualification("高级教师资格证");
        teacher2.earnQualification("心理咨询师证");
        teacher2.earnQualification("教育技术能力证");

        // 6. 演示教师协作
        System.out.println("\n6. 教师协作演示：");
        teacher1.collaborateWith(teacher2);
        teacher1.participateInResearch("数学教学方法创新");
        teacher2.mentorStudent("小明", "英语口语");

        // 7. 显示详细信息
        System.out.println("\n7. 教师信息展示：");
        teacher1.displayInfo();
        teacher2.displayInfo();

        // 8. 演示工具方法
        System.out.println("\n8. 工具方法演示：");
        System.out.println(teacher1.getName() + " 是否成年：" + teacher1.isAdult());
        System.out.println(teacher2.getName() + " 距离退休还有：" + teacher2.yearsToRetirement() + " 年");

        // 9. 静态方法演示
        System.out.println("\n9. 静态方法演示：");
        System.out.println("总人数：" + Person.getTotalPersons());
        System.out.println("教师总数：" + Teacher.getTotalTeachers());
        Person.compareAge(teacher1, teacher2);

        // 10. toString方法演示
        System.out.println("\n10. toString方法演示：");
        System.out.println("teacher1: " + teacher1.toString());
        System.out.println("teacher2: " + teacher2.toString());

        // 11. 生日庆祝演示
        System.out.println("\n11. 生日庆祝演示：");
        teacher1.celebrateBirthday();

        System.out.println("\n=== Teacher类演示完成 ===");

        System.out.println("\n📚 学习要点总结：");
        System.out.println("1. Teacher类继承自Person抽象类");
        System.out.println("2. 必须实现所有抽象方法：work(), study(), getProfession(), calculateIncome()");
        System.out.println("3. 可以重写父类的普通方法：introduce(), displayInfo()");
        System.out.println("4. 可以添加自己特有的属性和方法");
        System.out.println("5. 通过super()调用父类构造方法");
        System.out.println("6. 体现了面向对象的继承和多态特性");
    }
}
