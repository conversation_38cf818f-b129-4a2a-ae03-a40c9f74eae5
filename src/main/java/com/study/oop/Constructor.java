package com.study.oop;

/**
 * 构造方法演示程序
 * 演示构造方法的定义、使用和特性
 *
 * 构造方法的特点：
 * 1. 方法名与类名相同
 * 2. 没有返回值类型（连void都没有）
 * 3. 在创建对象时自动调用
 * 4. 可以重载（提供多个不同参数的构造方法）
 * 5. 如果不定义构造方法，系统会提供默认的无参构造方法
 */
public class Constructor {

    // 成员变量
    private String name;
    private int age;
    private String email;

    /**
     * 无参构造方法
     * 提供默认值初始化
     */
    public Constructor(){
        this.name = "未知";
        this.age = 0;
        this.email = "未设置";
        System.out.println("无参构造方法被调用 - 使用默认值初始化");
    }

    /**
     * 有参构造方法（姓名和年龄）
     * @param name 姓名
     * @param age 年龄
     */
    public Constructor(String name, int age){
        this.name = name;
        this.age = age;
        this.email = "未设置";
        System.out.println("有参构造方法被调用 - 姓名: " + name + ", 年龄: " + age);
    }

    /**
     * 有参构造方法（全参数）
     * @param name 姓名
     * @param age 年龄
     * @param email 邮箱
     */
    public Constructor(String name, int age, String email){
        this.name = name;
        this.age = age;
        this.email = email;
        System.out.println("全参构造方法被调用 - 姓名: " + name + ", 年龄: " + age + ", 邮箱: " + email);
    }

    /**
     * 构造方法链调用示例
     * 只传入姓名，年龄使用默认值18
     * @param name 姓名
     */
    public Constructor(String name){
        this(name, 18); // 调用两参数的构造方法
        System.out.println("单参构造方法被调用 - 使用构造方法链");
    }

    // Getter方法
    public String getName() {
        return name;
    }

    public int getAge() {
        return age;
    }

    public String getEmail() {
        return email;
    }

    /**
     * 显示对象信息
     */
    public void displayInfo() {
        System.out.println("对象信息 - 姓名: " + name + ", 年龄: " + age + ", 邮箱: " + email);
    }

    /**
     * 主方法 - 演示不同构造方法的使用
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        System.out.println("=== 构造方法演示 ===\n");

        // 1. 使用无参构造方法
        System.out.println("1. 创建对象 - 无参构造方法:");
        Constructor obj1 = new Constructor();
        obj1.displayInfo();
        System.out.println();

        // 2. 使用两参数构造方法
        System.out.println("2. 创建对象 - 两参数构造方法:");
        Constructor obj2 = new Constructor("张三", 25);
        obj2.displayInfo();
        System.out.println();

        // 3. 使用全参数构造方法
        System.out.println("3. 创建对象 - 全参数构造方法:");
        Constructor obj3 = new Constructor("李四", 30, "<EMAIL>");
        obj3.displayInfo();
        System.out.println();

        // 4. 使用单参数构造方法（构造方法链）
        System.out.println("4. 创建对象 - 单参数构造方法（构造方法链）:");
        Constructor obj4 = new Constructor("王五");
        obj4.displayInfo();
        System.out.println();

        System.out.println("=== 构造方法演示完成 ===");
    }
}
